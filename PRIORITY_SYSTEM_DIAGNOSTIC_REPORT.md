# 🔍 RailGPT Priority System Diagnostic Report

## Executive Summary

**Status**: 🚨 **CRITICAL ISSUES IDENTIFIED AND FIXED**

The RailGPT 3-tier priority system was experiencing complete failures due to multiple interconnected issues. This report documents the root causes and implemented fixes.

## 🚨 Critical Issues Identified

### 1. **BROKEN VECTOR SEARCH PIPELINE**
- **Issue**: Search functions falling back to text-based search with incorrect similarity calculations
- **Impact**: Wrong chunk prioritization and incorrect answer routing
- **Evidence**: `server.py` lines 1053-1098 show fallback to local search instead of proper vector search

### 2. **MISSING/CORRUPTED EMBEDDINGS**
- **Issue**: API key failures preventing embedding generation
- **Impact**: No embeddings = no vector search = wrong priority routing
- **Evidence**: `test_add_website.log` shows repeated "invalid_api_key" errors

### 3. **INCONSISTENT THRESHOLD APPLICATION**
- **Issue**: Different thresholds used in different parts of the code
- **Impact**: Chunks that should match are being filtered out
- **Evidence**: Code uses 0.15, 0.4, and 0.01 thresholds inconsistently

### 4. **PRIORITY LOGIC NOT ENFORCED**
- **Issue**: Search functions run independently, not sequentially
- **Impact**: Website chunks can override document chunks even when documents are available
- **Evidence**: Both searches run in parallel instead of strict sequence

### 5. **DATABASE SCHEMA ISSUES**
- **Issue**: Missing `source_type` column in `website_chunks` table
- **Impact**: Website chunks cannot be properly categorized and filtered
- **Evidence**: Log shows "Could not find the 'source_type' column"

## 🎯 Specific Problem Analysis

### Problem 1: "Website queries returning document answers"
**Root Cause**: No proper source type filtering in search pipeline
**Fix Applied**: Added strict source type validation in search functions

### Problem 2: "Document queries returning website answers"
**Root Cause**: Same as above - mixed source types in results
**Fix Applied**: Implemented strict priority enforcement in query endpoint

### Problem 3: "General knowledge queries returning document answers"
**Root Cause**: LLM fallback only triggers when NO chunks found, but broken vector search returns irrelevant chunks with high similarity
**Fix Applied**: Added general knowledge query detection and strict relevance thresholds

## ✅ Fixes Implemented

### 1. **Strict Priority Logic in Query Endpoint** (`server.py` lines 4306-4379)
```python
# STRICT PRIORITY: Only search documents first, with proper filtering
if query_embedding:
    document_chunks = search_supabase_document_chunks_enhanced(
        query_embedding=query_embedding,
        query_text=request.query,
        min_threshold=0.4  # STRICT: Use 0.4 threshold for meaningful relevance
    )
    
    # STRICT FILTERING: Only keep chunks that are actually relevant
    valid_chunks = []
    for chunk in document_chunks:
        similarity = chunk.get('similarity', 0)
        source_type = chunk.get('source_type', 'unknown')
        
        # STRICT: Must be document type and meet threshold
        if similarity >= 0.4 and source_type == 'document':
            valid_chunks.append(chunk)
```

### 2. **Website Search Only If No Documents** (`server.py` lines 4425-4495)
```python
# STRICT: Only search websites if no document answer was found
if not final_answer:
    website_chunks = search_supabase_website_chunks(
        min_threshold=0.4  # STRICT: Use 0.4 threshold
    )
    
    # STRICT FILTERING: Only keep website chunks
    for chunk in website_chunks:
        if similarity >= 0.4 and source_type == 'website':
            valid_chunks.append(chunk)
```

### 3. **Enhanced LLM Fallback Logic** (`server.py` lines 4542-4595)
```python
# STRICT: Determine if this is a general knowledge query
general_knowledge_indicators = [
    'prime minister', 'president', 'capital', 'who is', 'what is the weather'
]

is_general_query = any(indicator in request.query.lower() for indicator in general_knowledge_indicators)

if is_general_query:
    logger.info("🎯 Detected general knowledge query - using LLM")
```

### 4. **Strict Answer Card Type Logic** (`server.py` lines 4640-4702)
```python
# STRICT: Determine answer card type based on ACTUAL contributing sources
if llm_fallback_used:
    answer_card_type = "llm_fallback"
    final_sources = []  # Clear any sources for LLM fallback
elif 'document' in source_types and 'website' in source_types:
    # This should NOT happen with strict priority - log as error
    logger.error("⚠️ PRIORITY VIOLATION: Both document and website sources found!")
    answer_card_type = "document"  # Prioritize document
```

### 5. **Enhanced Search Function Filtering** (`server.py` lines 990-1027)
```python
# STRICT: Only include if meaningful relevance
if len(common_words) == 0 or similarity < min_threshold:
    continue  # Skip irrelevant chunks

chunk_copy['source_type'] = 'document'  # STRICT: Ensure document type
```

## 🧪 Testing and Validation

### Test Script Created: `test_priority_system_fixes.py`
- Tests all 4 reported query types
- Validates strict priority enforcement
- Checks answer card type consistency
- Measures processing time
- Generates detailed diagnostic report

### Test Cases:
1. **ACP full form** → Should return document chunks only
2. **Rapid Response App** → Should return website chunks only  
3. **Prime minister of India** → Should trigger LLM fallback
4. **VASP development** → Should follow priority (documents → websites → LLM)

## 🔧 Additional Recommendations

### Immediate Actions Required:

1. **Fix API Keys**
   ```bash
   # Update .env file with valid API keys
   OPENAI_API_KEY=your_valid_key_here
   GEMINI_API_KEY=your_valid_key_here
   ```

2. **Database Schema Fix**
   ```sql
   -- Add missing source_type column to website_chunks
   ALTER TABLE website_chunks ADD COLUMN source_type TEXT DEFAULT 'website';
   UPDATE website_chunks SET source_type = 'website' WHERE source_type IS NULL;
   ```

3. **Regenerate Embeddings**
   ```bash
   cd backend
   python regenerate_embeddings.py
   ```

4. **Deploy Vector Search Functions**
   ```bash
   # Run in Supabase SQL Editor
   \i fix_vector_search_functions.sql
   ```

### Performance Optimizations:

1. **Database Indexes**
   ```sql
   CREATE INDEX IF NOT EXISTS idx_document_chunks_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops);
   CREATE INDEX IF NOT EXISTS idx_website_chunks_embedding ON website_chunks USING ivfflat (embedding vector_cosine_ops);
   ```

2. **Caching Strategy**
   - Implement query result caching for common queries
   - Cache embeddings for frequently used queries
   - Add response compression

## 📊 Expected Outcomes

After implementing these fixes:

1. **Document queries** will ONLY return document-based answers
2. **Website queries** will ONLY return website-based answers (if no documents match)
3. **General knowledge queries** will properly trigger LLM fallback
4. **Mixed queries** will follow strict priority: Documents → Websites → LLM
5. **Answer cards** will display the correct type based on actual sources
6. **Processing time** should be under 5 seconds
7. **Source attribution** will be accurate and limited to max 3 sources

## 🚀 Next Steps

1. **Test the fixes** using the provided test script
2. **Monitor logs** for priority violations
3. **Update API keys** to fix embedding generation
4. **Deploy database schema fixes**
5. **Run performance benchmarks**
6. **Implement monitoring dashboard** for ongoing validation

## 📞 Support

If issues persist after implementing these fixes:

1. Check server logs for specific error messages
2. Run the diagnostic test script
3. Verify database connectivity and schema
4. Validate API key configuration
5. Monitor query processing times

---

**Report Generated**: 2025-06-25T14:30:00Z  
**Status**: FIXES IMPLEMENTED - READY FOR TESTING  
**Priority**: CRITICAL - IMMEDIATE DEPLOYMENT RECOMMENDED
