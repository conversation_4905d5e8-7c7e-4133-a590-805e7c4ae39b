#!/usr/bin/env python3
"""
RailGPT Comprehensive Priority System Diagnostic
Identifies root causes of incorrect answer routing in the 3-tier priority system
"""

import sys
import json
import logging
from typing import Dict, List, Any, Optional
from supabase_client import supabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RailGPTDiagnostic:
    def __init__(self):
        self.issues = []
        self.recommendations = []
        
    def add_issue(self, category: str, severity: str, description: str, impact: str):
        """Add an issue to the diagnostic report."""
        self.issues.append({
            'category': category,
            'severity': severity,
            'description': description,
            'impact': impact
        })
        
    def add_recommendation(self, category: str, priority: str, action: str, details: str):
        """Add a recommendation to the diagnostic report."""
        self.recommendations.append({
            'category': category,
            'priority': priority,
            'action': action,
            'details': details
        })

    def check_database_structure(self) -> Dict[str, Any]:
        """Check the structure and content of document_chunks and website_chunks tables."""
        logger.info("🔍 Checking database structure and content...")
        
        results = {
            'document_chunks': {'count': 0, 'with_embeddings': 0, 'with_text': 0, 'sample_data': []},
            'website_chunks': {'count': 0, 'with_embeddings': 0, 'with_text': 0, 'sample_data': []},
            'vector_functions': {'available': [], 'missing': []},
            'schema_issues': []
        }
        
        # Check document_chunks table
        try:
            # Get total count
            doc_count_result = supabase.table('document_chunks').select('id', count='exact').execute()
            results['document_chunks']['count'] = doc_count_result.count if hasattr(doc_count_result, 'count') else 0
            
            # Get sample data with all fields
            doc_sample_result = supabase.table('document_chunks').select('*').limit(5).execute()
            if doc_sample_result.data:
                results['document_chunks']['sample_data'] = doc_sample_result.data
                
                # Count chunks with embeddings and text
                for chunk in doc_sample_result.data:
                    if chunk.get('embedding'):
                        results['document_chunks']['with_embeddings'] += 1
                    if chunk.get('text') and chunk.get('text').strip():
                        results['document_chunks']['with_text'] += 1
                        
            logger.info(f"📄 Document chunks: {results['document_chunks']['count']} total")
            logger.info(f"   With embeddings: {results['document_chunks']['with_embeddings']}/5 sample")
            logger.info(f"   With text: {results['document_chunks']['with_text']}/5 sample")
            
        except Exception as e:
            logger.error(f"❌ Error checking document_chunks: {str(e)}")
            self.add_issue('Database', 'CRITICAL', f'Cannot access document_chunks table: {str(e)}', 
                          'Document queries will fail completely')
            
        # Check website_chunks table
        try:
            # Get total count
            web_count_result = supabase.table('website_chunks').select('id', count='exact').execute()
            results['website_chunks']['count'] = web_count_result.count if hasattr(web_count_result, 'count') else 0
            
            # Get sample data with all fields
            web_sample_result = supabase.table('website_chunks').select('*').limit(5).execute()
            if web_sample_result.data:
                results['website_chunks']['sample_data'] = web_sample_result.data
                
                # Count chunks with embeddings and text
                for chunk in web_sample_result.data:
                    if chunk.get('embedding'):
                        results['website_chunks']['with_embeddings'] += 1
                    if chunk.get('text') and chunk.get('text').strip():
                        results['website_chunks']['with_text'] += 1
                        
            logger.info(f"🌐 Website chunks: {results['website_chunks']['count']} total")
            logger.info(f"   With embeddings: {results['website_chunks']['with_embeddings']}/5 sample")
            logger.info(f"   With text: {results['website_chunks']['with_text']}/5 sample")
            
        except Exception as e:
            logger.error(f"❌ Error checking website_chunks: {str(e)}")
            self.add_issue('Database', 'CRITICAL', f'Cannot access website_chunks table: {str(e)}', 
                          'Website queries will fail completely')
        
        # Check vector search functions
        vector_functions = [
            'search_document_chunks',
            'search_website_chunks', 
            'hybrid_search_document_chunks',
            'hybrid_search_website_chunks',
            'direct_search_document_chunks',
            'direct_search_website_chunks'
        ]
        
        for func_name in vector_functions:
            try:
                # Test function with dummy parameters
                result = supabase.rpc(func_name, {
                    'query_embedding': [0.1] * 768,
                    'match_threshold': 0.5,
                    'match_count': 1
                }).execute()
                
                if hasattr(result, 'data'):
                    results['vector_functions']['available'].append(func_name)
                    logger.info(f"✅ {func_name}: Available")
                else:
                    results['vector_functions']['missing'].append(func_name)
                    logger.warning(f"⚠️ {func_name}: Not working properly")
                    
            except Exception as e:
                results['vector_functions']['missing'].append(func_name)
                logger.error(f"❌ {func_name}: Error - {str(e)}")
                
        return results

    def test_priority_logic(self) -> Dict[str, Any]:
        """Test the 3-tier priority logic with specific queries."""
        logger.info("🎯 Testing 3-tier priority logic...")
        
        test_queries = [
            {
                'query': 'ACP full form',
                'expected_source': 'document',
                'description': 'Should find document chunks about ACP (Automatic Coach Pressure)'
            },
            {
                'query': 'Rapid Response App',
                'expected_source': 'website', 
                'description': 'Should find website chunks from rapidresponseapp.com'
            },
            {
                'query': 'prime minister of India',
                'expected_source': 'llm_fallback',
                'description': 'Should trigger LLM fallback for general knowledge'
            }
        ]
        
        results = {'test_results': [], 'priority_issues': []}
        
        # Import the query processing function
        try:
            from server import query as process_query_endpoint
            from pydantic import BaseModel
            
            class QueryRequest(BaseModel):
                query: str
                model: str = "gemini-2.0-flash"
                fallback_enabled: bool = True
                use_hybrid_search: bool = True
                extract_format: str = "paragraph"
                
        except ImportError as e:
            logger.error(f"❌ Cannot import query processing function: {str(e)}")
            self.add_issue('Code', 'CRITICAL', f'Cannot import query endpoint: {str(e)}',
                          'Query processing will fail completely')
            return results
            
        for test_case in test_queries:
            logger.info(f"🧪 Testing: '{test_case['query']}'")
            
            try:
                # Create request object
                request = QueryRequest(query=test_case['query'])
                
                # Process query (this would normally be async, but we'll call it directly)
                # Note: This is a simplified test - in reality we'd need to handle async properly
                logger.info(f"   Expected source: {test_case['expected_source']}")
                logger.info(f"   Description: {test_case['description']}")
                
                # For now, just log what we would test
                test_result = {
                    'query': test_case['query'],
                    'expected_source': test_case['expected_source'],
                    'actual_source': 'unknown',  # Would be determined by actual query
                    'priority_followed': False,  # Would be determined by actual query
                    'issues_found': []
                }
                
                results['test_results'].append(test_result)
                
            except Exception as e:
                logger.error(f"❌ Error testing query '{test_case['query']}': {str(e)}")
                self.add_issue('Priority Logic', 'HIGH', 
                              f"Query '{test_case['query']}' failed: {str(e)}",
                              'Priority system not working correctly')
        
        return results

    def analyze_embedding_issues(self) -> Dict[str, Any]:
        """Analyze embedding generation and storage issues."""
        logger.info("🔬 Analyzing embedding issues...")
        
        results = {'embedding_issues': [], 'api_key_status': {}}
        
        # Check API key configuration
        try:
            from llm_router import llm_router
            
            # Check different providers
            providers = ['openai', 'gemini', 'groq']
            for provider in providers:
                try:
                    key_status = llm_router.check_api_key_validity(provider)
                    results['api_key_status'][provider] = key_status
                    
                    if not key_status.get('valid', False):
                        logger.warning(f"⚠️ {provider} API key invalid: {key_status.get('message', 'Unknown error')}")
                        self.add_issue('API Keys', 'HIGH', 
                                      f'{provider} API key invalid: {key_status.get("message", "Unknown")}',
                                      'Embedding generation will fail for this provider')
                    else:
                        logger.info(f"✅ {provider} API key valid")
                        
                except Exception as e:
                    logger.error(f"❌ Error checking {provider} API key: {str(e)}")
                    results['api_key_status'][provider] = {'valid': False, 'error': str(e)}
                    
        except ImportError as e:
            logger.error(f"❌ Cannot import llm_router: {str(e)}")
            self.add_issue('Code', 'CRITICAL', f'Cannot import llm_router: {str(e)}',
                          'Embedding generation will fail completely')
            
        return results

    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive diagnostic report."""
        logger.info("📊 Generating comprehensive diagnostic report...")
        
        # Run all diagnostic checks
        db_results = self.check_database_structure()
        priority_results = self.test_priority_logic()
        embedding_results = self.analyze_embedding_issues()
        
        # Analyze findings and generate recommendations
        self._analyze_findings(db_results, priority_results, embedding_results)
        
        report = {
            'timestamp': '2025-06-25T14:30:00Z',
            'system_status': 'CRITICAL_ISSUES_FOUND',
            'database_analysis': db_results,
            'priority_logic_analysis': priority_results,
            'embedding_analysis': embedding_results,
            'issues_found': self.issues,
            'recommendations': self.recommendations,
            'summary': self._generate_summary()
        }
        
        return report
        
    def _analyze_findings(self, db_results, priority_results, embedding_results):
        """Analyze all findings and generate specific recommendations."""
        
        # Check for critical database issues
        if db_results['document_chunks']['count'] == 0:
            self.add_issue('Database', 'CRITICAL', 'No document chunks found in database',
                          'All document queries will return no results, causing incorrect LLM fallback')
            self.add_recommendation('Database', 'URGENT', 'Upload and process documents',
                                  'Use document upload API to add documents and ensure chunks are created')
                                  
        if db_results['website_chunks']['count'] == 0:
            self.add_issue('Database', 'CRITICAL', 'No website chunks found in database', 
                          'All website queries will return no results, causing incorrect priority routing')
            self.add_recommendation('Database', 'URGENT', 'Extract and store website content',
                                  'Use website extraction API to add rapidresponseapp.com and other sites')
        
        # Check for embedding issues
        doc_sample = db_results['document_chunks']['sample_data']
        if doc_sample and db_results['document_chunks']['with_embeddings'] == 0:
            self.add_issue('Embeddings', 'CRITICAL', 'Document chunks have no embeddings',
                          'Vector search will fail, causing fallback to text search or LLM')
            self.add_recommendation('Embeddings', 'URGENT', 'Regenerate document embeddings',
                                  'Run embedding generation script for all document chunks')
                                  
        web_sample = db_results['website_chunks']['sample_data'] 
        if web_sample and db_results['website_chunks']['with_embeddings'] == 0:
            self.add_issue('Embeddings', 'CRITICAL', 'Website chunks have no embeddings',
                          'Vector search will fail, causing incorrect priority routing')
            self.add_recommendation('Embeddings', 'URGENT', 'Regenerate website embeddings',
                                  'Run embedding generation script for all website chunks')
        
        # Check vector search functions
        missing_functions = db_results['vector_functions']['missing']
        if missing_functions:
            self.add_issue('Vector Search', 'HIGH', f'Missing vector search functions: {missing_functions}',
                          'Vector search will fail, causing fallback to less accurate methods')
            self.add_recommendation('Vector Search', 'HIGH', 'Deploy missing vector search functions',
                                  'Run SQL scripts to create missing Supabase RPC functions')

    def _generate_summary(self) -> Dict[str, Any]:
        """Generate executive summary of findings."""
        critical_issues = [i for i in self.issues if i['severity'] == 'CRITICAL']
        high_issues = [i for i in self.issues if i['severity'] == 'HIGH']
        
        return {
            'total_issues': len(self.issues),
            'critical_issues': len(critical_issues),
            'high_priority_issues': len(high_issues),
            'root_cause_analysis': self._identify_root_causes(),
            'immediate_actions_required': [r for r in self.recommendations if r['priority'] == 'URGENT']
        }
        
    def _identify_root_causes(self) -> List[str]:
        """Identify the root causes of priority system failures."""
        root_causes = []
        
        # Analyze patterns in issues
        database_issues = [i for i in self.issues if i['category'] == 'Database']
        embedding_issues = [i for i in self.issues if i['category'] == 'Embeddings']
        
        if database_issues:
            root_causes.append("Empty or corrupted database tables preventing proper chunk retrieval")
            
        if embedding_issues:
            root_causes.append("Missing or invalid embeddings preventing vector similarity search")
            
        if not database_issues and not embedding_issues:
            root_causes.append("Logic errors in priority system implementation")
            
        return root_causes

def main():
    """Main diagnostic function."""
    print("🔍 RailGPT Comprehensive Priority System Diagnostic")
    print("=" * 60)
    
    diagnostic = RailGPTDiagnostic()
    report = diagnostic.generate_report()
    
    # Save report to file
    with open('diagnostic_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Print summary
    print(f"\n📊 DIAGNOSTIC SUMMARY")
    print(f"   System Status: {report['system_status']}")
    print(f"   Total Issues Found: {report['summary']['total_issues']}")
    print(f"   Critical Issues: {report['summary']['critical_issues']}")
    print(f"   High Priority Issues: {report['summary']['high_priority_issues']}")
    
    print(f"\n🔍 ROOT CAUSES IDENTIFIED:")
    for cause in report['summary']['root_cause_analysis']:
        print(f"   • {cause}")
    
    print(f"\n⚡ IMMEDIATE ACTIONS REQUIRED:")
    for action in report['summary']['immediate_actions_required']:
        print(f"   • {action['action']}: {action['details']}")
    
    print(f"\n📄 Full report saved to: diagnostic_report.json")
    
    return len(report['summary']['immediate_actions_required']) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
