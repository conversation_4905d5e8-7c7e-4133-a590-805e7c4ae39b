#!/usr/bin/env python3
"""
Test script to validate the RailGPT 3-tier priority system fixes
Tests the specific issues reported by the user
"""

import sys
import json
import logging
import requests
import time
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PrioritySystemTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        
    def test_query(self, query: str, expected_source: str, description: str) -> Dict[str, Any]:
        """Test a single query and validate the response."""
        logger.info(f"🧪 Testing: '{query}'")
        logger.info(f"   Expected source: {expected_source}")
        logger.info(f"   Description: {description}")
        
        try:
            # Send query to the API
            payload = {
                "query": query,
                "model": "gemini-2.0-flash",
                "fallback_enabled": True,
                "use_hybrid_search": True,
                "extract_format": "paragraph"
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/query",
                json=payload,
                timeout=30
            )
            processing_time = time.time() - start_time
            
            if response.status_code != 200:
                logger.error(f"❌ API error: {response.status_code} - {response.text}")
                return {
                    'query': query,
                    'expected_source': expected_source,
                    'actual_source': 'error',
                    'success': False,
                    'error': f"HTTP {response.status_code}",
                    'processing_time': processing_time
                }
            
            result = response.json()
            
            # Analyze the response
            actual_source = self._determine_actual_source(result)
            success = self._validate_priority_logic(result, expected_source, actual_source)
            
            test_result = {
                'query': query,
                'expected_source': expected_source,
                'actual_source': actual_source,
                'success': success,
                'answer_length': len(result.get('answer', '')),
                'sources_count': len(result.get('sources', [])),
                'llm_fallback_used': result.get('llm_fallback_used', False),
                'answer_card_type': result.get('answer_card_type', 'unknown'),
                'processing_time': processing_time,
                'issues_found': []
            }
            
            # Detailed validation
            self._validate_response_structure(result, test_result)
            
            if success:
                logger.info(f"✅ Test PASSED: {query}")
            else:
                logger.error(f"❌ Test FAILED: {query}")
                logger.error(f"   Expected: {expected_source}, Got: {actual_source}")
            
            return test_result
            
        except Exception as e:
            logger.error(f"❌ Test error for '{query}': {str(e)}")
            return {
                'query': query,
                'expected_source': expected_source,
                'actual_source': 'error',
                'success': False,
                'error': str(e),
                'processing_time': 0
            }
    
    def _determine_actual_source(self, result: Dict[str, Any]) -> str:
        """Determine the actual source type from the API response."""
        llm_fallback_used = result.get('llm_fallback_used', False)
        sources = result.get('sources', [])
        answer_card_type = result.get('answer_card_type', 'unknown')
        
        if llm_fallback_used:
            return 'llm_fallback'
        
        if not sources:
            return 'llm_fallback'
        
        # Check source types
        source_types = set()
        for source in sources:
            source_type = source.get('source_type', 'unknown')
            source_types.add(source_type)
        
        if 'document' in source_types and 'website' in source_types:
            return 'both'  # This should NOT happen with strict priority
        elif 'document' in source_types:
            return 'document'
        elif 'website' in source_types:
            return 'website'
        else:
            return 'unknown'
    
    def _validate_priority_logic(self, result: Dict[str, Any], expected: str, actual: str) -> bool:
        """Validate that the priority logic is working correctly."""
        # Direct match
        if expected == actual:
            return True
        
        # Special cases for priority violations
        if expected == 'document' and actual == 'website':
            # This is a priority violation - website should not override document
            return False
        
        if expected == 'website' and actual == 'document':
            # This could be acceptable if documents are more relevant
            # But we need to check if this is the intended behavior
            return True  # For now, accept this as documents have higher priority
        
        if expected == 'llm_fallback' and actual in ['document', 'website']:
            # This is a priority violation - irrelevant chunks should not override LLM
            return False
        
        return False
    
    def _validate_response_structure(self, result: Dict[str, Any], test_result: Dict[str, Any]):
        """Validate the response structure and add issues if found."""
        issues = test_result['issues_found']
        
        # Check for required fields
        required_fields = ['answer', 'sources', 'llm_fallback_used']
        for field in required_fields:
            if field not in result:
                issues.append(f"Missing required field: {field}")
        
        # Check source consistency
        sources = result.get('sources', [])
        llm_fallback_used = result.get('llm_fallback_used', False)
        
        if llm_fallback_used and sources:
            issues.append("LLM fallback used but sources are present - should be empty")
        
        if not llm_fallback_used and not sources:
            issues.append("No LLM fallback but no sources - inconsistent state")
        
        # Check answer card type consistency
        answer_card_type = result.get('answer_card_type', 'unknown')
        if llm_fallback_used and answer_card_type != 'llm_fallback':
            issues.append(f"LLM fallback used but card type is '{answer_card_type}' instead of 'llm_fallback'")
        
        # Check source types consistency
        if sources:
            source_types = set(s.get('source_type', 'unknown') for s in sources)
            if len(source_types) > 1:
                issues.append(f"Mixed source types in response: {source_types} - violates priority system")
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive tests for all reported issues."""
        logger.info("🚀 Starting comprehensive RailGPT priority system test")
        logger.info("=" * 60)
        
        # Test cases based on user's reported issues
        test_cases = [
            {
                'query': 'ACP full form',
                'expected_source': 'document',
                'description': 'Should find document chunks about ACP (Automatic Coach Pressure)'
            },
            {
                'query': 'what is ACP in railways',
                'expected_source': 'document',
                'description': 'Should find document chunks about ACP system'
            },
            {
                'query': 'Rapid Response App',
                'expected_source': 'website',
                'description': 'Should find website chunks from rapidresponseapp.com'
            },
            {
                'query': 'rapid response application features',
                'expected_source': 'website',
                'description': 'Should find website chunks about the app features'
            },
            {
                'query': 'prime minister of India',
                'expected_source': 'llm_fallback',
                'description': 'Should trigger LLM fallback for general knowledge'
            },
            {
                'query': 'who is the current president of USA',
                'expected_source': 'llm_fallback',
                'description': 'Should trigger LLM fallback for general knowledge'
            },
            {
                'query': 'VASP development',
                'expected_source': 'document',  # Assuming this is in documents
                'description': 'Should find relevant chunks (documents or websites)'
            },
            {
                'query': 'WDG4G locomotive details',
                'expected_source': 'llm_fallback',  # Assuming this triggers fallback
                'description': 'Should trigger LLM fallback if no relevant content'
            }
        ]
        
        # Run all tests
        for test_case in test_cases:
            result = self.test_query(
                test_case['query'],
                test_case['expected_source'],
                test_case['description']
            )
            self.test_results.append(result)
            
            # Add delay between tests
            time.sleep(1)
        
        # Generate summary
        summary = self._generate_test_summary()
        
        # Save detailed results
        detailed_report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'summary': summary,
            'test_results': self.test_results
        }
        
        with open('priority_system_test_results.json', 'w') as f:
            json.dump(detailed_report, f, indent=2)
        
        return detailed_report
    
    def _generate_test_summary(self) -> Dict[str, Any]:
        """Generate a summary of test results."""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r.get('success', False))
        failed_tests = total_tests - passed_tests
        
        # Categorize failures
        priority_violations = []
        structural_issues = []
        
        for result in self.test_results:
            if not result.get('success', False):
                query = result['query']
                expected = result['expected_source']
                actual = result['actual_source']
                
                if expected != actual:
                    priority_violations.append({
                        'query': query,
                        'expected': expected,
                        'actual': actual
                    })
                
                issues = result.get('issues_found', [])
                if issues:
                    structural_issues.extend([f"{query}: {issue}" for issue in issues])
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'priority_violations': priority_violations,
            'structural_issues': structural_issues,
            'avg_processing_time': sum(r.get('processing_time', 0) for r in self.test_results) / total_tests if total_tests > 0 else 0
        }

def main():
    """Main test function."""
    print("🧪 RailGPT Priority System Validation Test")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ RailGPT server is not responding properly")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to RailGPT server: {str(e)}")
        print("💡 Make sure the backend server is running on http://localhost:8000")
        return False
    
    print("✅ Server is running, starting tests...")
    
    # Run tests
    tester = PrioritySystemTester()
    report = tester.run_comprehensive_test()
    
    # Print summary
    summary = report['summary']
    print(f"\n📊 TEST SUMMARY")
    print(f"   Total Tests: {summary['total_tests']}")
    print(f"   Passed: {summary['passed_tests']}")
    print(f"   Failed: {summary['failed_tests']}")
    print(f"   Success Rate: {summary['success_rate']:.1f}%")
    print(f"   Avg Processing Time: {summary['avg_processing_time']:.2f}s")
    
    if summary['priority_violations']:
        print(f"\n⚠️ PRIORITY VIOLATIONS FOUND:")
        for violation in summary['priority_violations']:
            print(f"   • '{violation['query']}': Expected {violation['expected']}, Got {violation['actual']}")
    
    if summary['structural_issues']:
        print(f"\n🔧 STRUCTURAL ISSUES FOUND:")
        for issue in summary['structural_issues'][:5]:  # Show first 5
            print(f"   • {issue}")
    
    print(f"\n📄 Detailed report saved to: priority_system_test_results.json")
    
    return summary['failed_tests'] == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
